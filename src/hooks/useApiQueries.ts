import {
  useQuery,
  useInfiniteQuery,
  useMutation,
  useQueryClient,
} from "@tanstack/react-query";

import type {
  UseQueryOptions,
  UseInfiniteQueryOptions,
  UseMutationOptions,
} from "@tanstack/react-query";

import {
  mangaService,
  chapterService,
  categoryService,
  userService,
} from "../services";

import type {
  MangaFilters,
  LoginCredentials,
  RegisterData,
  User,
  ApiError,
} from "../types";

// Query Keys Factory
export const queryKeys = {
  // Manga queries
  manga: {
    all: ["manga"] as const,
    lists: () => [...queryKeys.manga.all, "list"] as const,
    list: (filters: MangaFilters) =>
      [...queryKeys.manga.lists(), filters] as const,
    details: () => [...queryKeys.manga.all, "detail"] as const,
    detail: (slug: string) => [...queryKeys.manga.details(), slug] as const,
    chapters: (slug: string) =>
      [...queryKeys.manga.detail(slug), "chapters"] as const,
    popular: (page?: number) =>
      [...queryKeys.manga.all, "popular", page] as const,
    trending: (page?: number) =>
      [...queryKeys.manga.all, "trending", page] as const,
    recent: (page?: number) =>
      [...queryKeys.manga.all, "recent", page] as const,
    completed: (page?: number) =>
      [...queryKeys.manga.all, "completed", page] as const,
    search: (query: string, page?: number) =>
      [...queryKeys.manga.all, "search", query, page] as const,
    byGenre: (genre: string, page?: number) =>
      [...queryKeys.manga.all, "genre", genre, page] as const,
  },

  // Chapter queries
  chapter: {
    all: ["chapter"] as const,
    content: (mangaSlug: string, chapterSlug: string) =>
      [...queryKeys.chapter.all, "content", mangaSlug, chapterSlug] as const,
    navigation: (mangaSlug: string, chapterSlug: string) =>
      [...queryKeys.chapter.all, "navigation", mangaSlug, chapterSlug] as const,
    images: (mangaSlug: string, chapterSlug: string) =>
      [...queryKeys.chapter.all, "images", mangaSlug, chapterSlug] as const,
  },

  // Category queries
  category: {
    all: ["category"] as const,
    list: () => [...queryKeys.category.all, "list"] as const,
    detail: (slug: string) =>
      [...queryKeys.category.all, "detail", slug] as const,
    popular: () => [...queryKeys.category.all, "popular"] as const,
    trending: () => [...queryKeys.category.all, "trending"] as const,
  },

  // User queries
  user: {
    all: ["user"] as const,
    profile: () => [...queryKeys.user.all, "profile"] as const,
    bookmarks: () => [...queryKeys.user.all, "bookmarks"] as const,
    history: () => [...queryKeys.user.all, "history"] as const,
    preferences: () => [...queryKeys.user.all, "preferences"] as const,
  },
} as const;

// Manga Hooks
export const useMangaListQuery = (
  filters: MangaFilters = {},
  options?: UseQueryOptions<any, ApiError>
) => {
  return useQuery({
    queryKey: queryKeys.manga.list(filters),
    queryFn: () => mangaService.getMangaList(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
};

export const useMangaInfiniteQuery = (
  filters: MangaFilters = {},
  options?: UseInfiniteQueryOptions<any, ApiError>
) => {
  return useInfiniteQuery({
    queryKey: queryKeys.manga.list(filters),
    queryFn: ({ pageParam = 1 }) =>
      mangaService.getMangaList({ ...filters, page: pageParam as number }),
    getNextPageParam: (lastPage) => {
      if (lastPage?.data?.params?.pagination) {
        const { currentPage, totalPages } = lastPage.data.params.pagination;
        return currentPage < totalPages ? currentPage + 1 : undefined;
      }
      return undefined;
    },
    initialPageParam: 1 as number,
    staleTime: 5 * 60 * 1000,
    ...options,
  });
};

export const useMangaDetailQuery = (
  slug: string,
  options?: UseQueryOptions<any, ApiError>
) => {
  return useQuery({
    queryKey: queryKeys.manga.detail(slug),
    queryFn: () => mangaService.getMangaDetails(slug),
    enabled: !!slug,
    staleTime: 10 * 60 * 1000, // 10 minutes
    ...options,
  });
};

export const useMangaChaptersQuery = (
  slug: string,
  options?: UseQueryOptions<any, ApiError>
) => {
  return useQuery({
    queryKey: queryKeys.manga.chapters(slug),
    queryFn: () => mangaService.getMangaChapters(slug),
    enabled: !!slug,
    staleTime: 10 * 60 * 1000,
    ...options,
  });
};

export const usePopularMangaQuery = (
  page: number = 1,
  options?: UseQueryOptions<any, ApiError>
) => {
  return useQuery({
    queryKey: queryKeys.manga.popular(page),
    queryFn: () => mangaService.getPopularManga(page),
    staleTime: 15 * 60 * 1000, // 15 minutes
    ...options,
  });
};

export const useTrendingMangaQuery = (
  page: number = 1,
  options?: UseQueryOptions<any, ApiError>
) => {
  return useQuery({
    queryKey: queryKeys.manga.trending(page),
    queryFn: () => mangaService.getTrendingManga(page),
    staleTime: 15 * 60 * 1000,
    ...options,
  });
};

export const useRecentMangaQuery = (
  page: number = 1,
  options?: UseQueryOptions<any, ApiError>
) => {
  return useQuery({
    queryKey: queryKeys.manga.recent(page),
    queryFn: () => mangaService.getRecentlyUpdated(page),
    staleTime: 5 * 60 * 1000,
    ...options,
  });
};

export const useSearchMangaQuery = (
  query: string,
  page: number = 1,
  options?: UseQueryOptions<any, ApiError>
) => {
  return useQuery({
    queryKey: queryKeys.manga.search(query, page),
    queryFn: () => mangaService.searchManga(query, page),
    enabled: !!query && query.length > 2,
    staleTime: 5 * 60 * 1000,
    ...options,
  });
};

// Chapter Hooks
export const useChapterContentQuery = (
  mangaSlug: string,
  chapterSlug: string,
  options?: UseQueryOptions<any, ApiError>
) => {
  return useQuery({
    queryKey: queryKeys.chapter.content(mangaSlug, chapterSlug),
    queryFn: () => chapterService.getChapterContent(mangaSlug, chapterSlug),
    enabled: !!mangaSlug && !!chapterSlug,
    staleTime: 30 * 60 * 1000, // 30 minutes
    ...options,
  });
};

export const useChapterNavigationQuery = (
  mangaSlug: string,
  chapterSlug: string,
  options?: UseQueryOptions<any, ApiError>
) => {
  return useQuery({
    queryKey: queryKeys.chapter.navigation(mangaSlug, chapterSlug),
    queryFn: () => chapterService.getChapterNavigation(mangaSlug, chapterSlug),
    enabled: !!mangaSlug && !!chapterSlug,
    staleTime: 30 * 60 * 1000,
    ...options,
  });
};

// Category Hooks
export const useCategoriesQuery = (
  options?: UseQueryOptions<any, ApiError>
) => {
  return useQuery({
    queryKey: queryKeys.category.list(),
    queryFn: () => categoryService.getCategories(),
    staleTime: 60 * 60 * 1000, // 1 hour
    ...options,
  });
};

export const usePopularCategoriesQuery = (
  limit: number = 10,
  options?: UseQueryOptions<any, ApiError>
) => {
  return useQuery({
    queryKey: queryKeys.category.popular(),
    queryFn: () => categoryService.getPopularCategories(limit),
    staleTime: 60 * 60 * 1000,
    ...options,
  });
};

// User Hooks
export const useUserProfileQuery = (
  options?: UseQueryOptions<any, ApiError>
) => {
  return useQuery({
    queryKey: queryKeys.user.profile(),
    queryFn: () => userService.getProfile(),
    enabled: userService.isAuthenticated(),
    staleTime: 10 * 60 * 1000,
    ...options,
  });
};

export const useUserBookmarksQuery = (
  options?: UseQueryOptions<any, ApiError>
) => {
  return useQuery({
    queryKey: queryKeys.user.bookmarks(),
    queryFn: () => userService.getBookmarks(),
    enabled: userService.isAuthenticated(),
    staleTime: 5 * 60 * 1000,
    ...options,
  });
};

export const useUserHistoryQuery = (
  options?: UseQueryOptions<any, ApiError>
) => {
  return useQuery({
    queryKey: queryKeys.user.history(),
    queryFn: () => userService.getReadingHistory(),
    enabled: userService.isAuthenticated(),
    staleTime: 5 * 60 * 1000,
    ...options,
  });
};

// Mutation Hooks
export const useLoginMutation = (
  options?: UseMutationOptions<any, ApiError, LoginCredentials>
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (credentials: LoginCredentials) =>
      userService.login(credentials),
    onSuccess: () => {
      // Invalidate and refetch user-related queries
      queryClient.invalidateQueries({ queryKey: queryKeys.user.all });
    },
    ...options,
  });
};

export const useRegisterMutation = (
  options?: UseMutationOptions<any, ApiError, RegisterData>
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: RegisterData) => userService.register(data),
    onSuccess: () => {
      // Invalidate and refetch user-related queries
      queryClient.invalidateQueries({ queryKey: queryKeys.user.all });
    },
    ...options,
  });
};

export const useLogoutMutation = (
  options?: UseMutationOptions<any, ApiError, void>
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: () => userService.logout(),
    onSuccess: () => {
      // Clear all user-related queries
      queryClient.removeQueries({ queryKey: queryKeys.user.all });
      queryClient.clear();
    },
    ...options,
  });
};

export const useUpdateProfileMutation = (
  options?: UseMutationOptions<any, ApiError, Partial<User>>
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: Partial<User>) => userService.updateProfile(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.user.profile() });
    },
    ...options,
  });
};

export const useBookmarkMutation = () => {
  const queryClient = useQueryClient();

  const addBookmark = useMutation({
    mutationFn: (mangaId: string) => userService.addBookmark(mangaId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.user.bookmarks() });
    },
  });

  const removeBookmark = useMutation({
    mutationFn: (mangaId: string) => userService.removeBookmark(mangaId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.user.bookmarks() });
    },
  });

  const toggleBookmark = useMutation({
    mutationFn: ({
      mangaId,
      isBookmarked,
    }: {
      mangaId: string;
      isBookmarked: boolean;
    }) => userService.toggleBookmark(mangaId, isBookmarked),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.user.bookmarks() });
    },
  });

  return { addBookmark, removeBookmark, toggleBookmark };
};

export const useReadingProgressMutation = (
  options?: UseMutationOptions<
    any,
    ApiError,
    {
      mangaId: string;
      chapterId: string;
      pageNumber: number;
    }
  >
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ mangaId, chapterId, pageNumber }) =>
      userService.updateReadingProgress(mangaId, chapterId, pageNumber),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.user.history() });
    },
    ...options,
  });
};
