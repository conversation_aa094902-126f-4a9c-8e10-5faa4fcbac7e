// Additional convenience hooks
import { useQuery } from "@tanstack/react-query";
import { mangaService } from "../services";

// Re-export the new comprehensive API hooks
export {
  // Query hooks
  useMangaListQuery,
  useMangaInfiniteQuery,
  useMangaDetailQuery,
  useMangaChaptersQuery,
  usePopularMangaQuery,
  useTrendingMangaQuery,
  useRecentMangaQuery,
  useSearchMangaQuery,

  // Mutation hooks
  useBookmarkMutation,
  useReadingProgressMutation,

  // Query keys
  queryKeys as mangaKeys,
} from "./useApiQueries";

// Import the hook for legacy alias
import { useMangaDetailQuery } from "./useApiQueries";

// Legacy exports for backward compatibility
export const useMangaQuery = useMangaDetailQuery;

/**
 * Hook for getting manga recommendations
 */
export const useMangaRecommendationsQuery = (mangaSlug: string) => {
  return useQuery({
    queryKey: ["manga", "recommendations", mangaSlug],
    queryFn: () => mangaService.getRecommendations(mangaSlug),
    enabled: !!mangaSlug,
    staleTime: 30 * 60 * 1000, // 30 minutes
  });
};

/**
 * Hook for advanced manga filtering
 */
export const useAdvancedMangaQuery = (options: {
  genres?: string[];
  status?: string;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
  page?: number;
  limit?: number;
}) => {
  return useQuery({
    queryKey: ["manga", "advanced", options],
    queryFn: () => mangaService.getAdvancedMangaList(options),
    staleTime: 5 * 60 * 1000,
  });
};
