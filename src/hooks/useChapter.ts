// Additional convenience hooks
import { useQuery } from "@tanstack/react-query";
import { chapterService } from "../services";

// Re-export the new comprehensive API hooks
export {
  useChapterContentQuery,
  useChapterNavigationQuery,
  queryKeys as chapterKeys,
} from "./useApiQueries";

// Legacy exports for backward compatibility
export const useChapterQuery = (mangaSlug: string, chapterSlug: string) => {
  return useQuery({
    queryKey: ["chapters", "detail", mangaSlug, chapterSlug],
    queryFn: () => chapterService.getChapterContent(mangaSlug, chapterSlug),
    enabled: !!mangaSlug && !!chapterSlug,
    staleTime: 10 * 60 * 1000, // 10 minutes - chapters don't change often
  });
};

/**
 * Hook for getting chapter images/pages
 */
export const useChapterImagesQuery = (
  mangaSlug: string,
  chapterSlug: string
) => {
  return useQuery({
    queryKey: ["chapters", "images", mangaSlug, chapterSlug],
    queryFn: () => chapterService.getChapterImages(mangaSlug, chapterSlug),
    enabled: !!mangaSlug && !!chapterSlug,
    staleTime: 30 * 60 * 1000, // 30 minutes
  });
};

/**
 * Hook for getting next chapter
 */
export const useNextChapterQuery = (
  mangaSlug: string,
  currentChapterSlug: string
) => {
  return useQuery({
    queryKey: ["chapters", "next", mangaSlug, currentChapterSlug],
    queryFn: () => chapterService.getNextChapter(mangaSlug, currentChapterSlug),
    enabled: !!mangaSlug && !!currentChapterSlug,
    staleTime: 30 * 60 * 1000,
  });
};

/**
 * Hook for getting previous chapter
 */
export const usePreviousChapterQuery = (
  mangaSlug: string,
  currentChapterSlug: string
) => {
  return useQuery({
    queryKey: ["chapters", "previous", mangaSlug, currentChapterSlug],
    queryFn: () =>
      chapterService.getPreviousChapter(mangaSlug, currentChapterSlug),
    enabled: !!mangaSlug && !!currentChapterSlug,
    staleTime: 30 * 60 * 1000,
  });
};
