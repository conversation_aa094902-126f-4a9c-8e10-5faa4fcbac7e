import React, { useState } from 'react';
import {
  useMangaListQuery,
  useMangaInfiniteQuery,
  useMangaDetailQuery,
  useSearchMangaQuery,
  useCategoriesQuery,
  useLoginMutation,
  useBookmarkMutation,
} from '../hooks/useApiQueries';

/**
 * Example component demonstrating the usage of the new API service layer
 * This shows various patterns for using the TanStack Query hooks
 */
export const ApiServiceExample: React.FC = () => {
  const [selectedManga, setSelectedManga] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [loginCredentials, setLoginCredentials] = useState({
    email: '',
    password: '',
  });

  // Example 1: Basic manga list query
  const {
    data: mangaList,
    isLoading: mangaListLoading,
    error: mangaListError,
  } = useMangaListQuery({
    page: 1,
    limit: 20,
  });

  // Example 2: Infinite query for pagination
  const {
    data: infiniteManga,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useMangaInfiniteQuery({
    status: 'ongoing',
  });

  // Example 3: Conditional query (only runs when selectedManga is set)
  const {
    data: mangaDetail,
    isLoading: detailLoading,
  } = useMangaDetailQuery(selectedManga, {
    enabled: !!selectedManga,
  });

  // Example 4: Search query with debouncing
  const {
    data: searchResults,
    isLoading: searchLoading,
  } = useSearchMangaQuery(searchQuery, 1, {
    enabled: searchQuery.length > 2, // Only search if query is longer than 2 chars
  });

  // Example 5: Categories query
  const {
    data: categories,
    isLoading: categoriesLoading,
  } = useCategoriesQuery();

  // Example 6: Login mutation
  const loginMutation = useLoginMutation({
    onSuccess: (data) => {
      console.log('Login successful:', data);
      alert('Login successful!');
    },
    onError: (error) => {
      console.error('Login failed:', error);
      alert(`Login failed: ${error.message}`);
    },
  });

  // Example 7: Bookmark mutation
  const { addBookmark, removeBookmark, toggleBookmark } = useBookmarkMutation();

  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault();
    loginMutation.mutate(loginCredentials);
  };

  const handleBookmarkToggle = (mangaId: string, isCurrentlyBookmarked: boolean) => {
    toggleBookmark.mutate({
      mangaId,
      isBookmarked: isCurrentlyBookmarked,
    });
  };

  return (
    <div className="p-6 max-w-4xl mx-auto space-y-8">
      <h1 className="text-3xl font-bold text-center mb-8">
        API Service Examples
      </h1>

      {/* Example 1: Basic Manga List */}
      <section className="border rounded-lg p-4">
        <h2 className="text-xl font-semibold mb-4">1. Basic Manga List</h2>
        {mangaListLoading && <p>Loading manga list...</p>}
        {mangaListError && (
          <p className="text-red-500">Error: {mangaListError.message}</p>
        )}
        {mangaList?.data?.items && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {mangaList.data.items.slice(0, 8).map((manga: any) => (
              <div
                key={manga.id}
                className="border rounded p-2 cursor-pointer hover:bg-gray-100"
                onClick={() => setSelectedManga(manga.slug)}
              >
                <img
                  src={manga.thumb_url}
                  alt={manga.name}
                  className="w-full h-32 object-cover rounded mb-2"
                />
                <p className="text-sm font-medium truncate">{manga.name}</p>
              </div>
            ))}
          </div>
        )}
      </section>

      {/* Example 2: Infinite Scrolling */}
      <section className="border rounded-lg p-4">
        <h2 className="text-xl font-semibold mb-4">2. Infinite Scrolling</h2>
        <div className="max-h-64 overflow-y-auto">
          {infiniteManga?.pages.map((page, pageIndex) => (
            <div key={pageIndex} className="mb-4">
              {page.data?.items?.map((manga: any) => (
                <div key={manga.id} className="flex items-center space-x-3 mb-2">
                  <img
                    src={manga.thumb_url}
                    alt={manga.name}
                    className="w-12 h-12 object-cover rounded"
                  />
                  <span className="text-sm">{manga.name}</span>
                </div>
              ))}
            </div>
          ))}
        </div>
        {hasNextPage && (
          <button
            onClick={() => fetchNextPage()}
            disabled={isFetchingNextPage}
            className="mt-4 px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50"
          >
            {isFetchingNextPage ? 'Loading...' : 'Load More'}
          </button>
        )}
      </section>

      {/* Example 3: Manga Detail */}
      <section className="border rounded-lg p-4">
        <h2 className="text-xl font-semibold mb-4">3. Manga Detail</h2>
        {selectedManga ? (
          <div>
            {detailLoading && <p>Loading manga details...</p>}
            {mangaDetail?.data && (
              <div className="flex space-x-4">
                <img
                  src={mangaDetail.data.thumb_url}
                  alt={mangaDetail.data.name}
                  className="w-32 h-48 object-cover rounded"
                />
                <div>
                  <h3 className="text-lg font-semibold">{mangaDetail.data.name}</h3>
                  <p className="text-sm text-gray-600 mt-2">
                    {mangaDetail.data.content?.substring(0, 200)}...
                  </p>
                  <button
                    onClick={() => handleBookmarkToggle(mangaDetail.data.id, false)}
                    className="mt-4 px-4 py-2 bg-green-500 text-white rounded"
                  >
                    Add to Bookmarks
                  </button>
                </div>
              </div>
            )}
          </div>
        ) : (
          <p className="text-gray-500">Click on a manga above to see details</p>
        )}
      </section>

      {/* Example 4: Search */}
      <section className="border rounded-lg p-4">
        <h2 className="text-xl font-semibold mb-4">4. Search Manga</h2>
        <input
          type="text"
          placeholder="Search manga..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="w-full p-2 border rounded mb-4"
        />
        {searchLoading && <p>Searching...</p>}
        {searchResults?.data?.items && (
          <div className="space-y-2">
            {searchResults.data.items.slice(0, 5).map((manga: any) => (
              <div key={manga.id} className="flex items-center space-x-3">
                <img
                  src={manga.thumb_url}
                  alt={manga.name}
                  className="w-10 h-10 object-cover rounded"
                />
                <span className="text-sm">{manga.name}</span>
              </div>
            ))}
          </div>
        )}
      </section>

      {/* Example 5: Categories */}
      <section className="border rounded-lg p-4">
        <h2 className="text-xl font-semibold mb-4">5. Categories</h2>
        {categoriesLoading && <p>Loading categories...</p>}
        {categories?.data && (
          <div className="flex flex-wrap gap-2">
            {categories.data.slice(0, 10).map((category: any) => (
              <span
                key={category.id}
                className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
              >
                {category.name}
              </span>
            ))}
          </div>
        )}
      </section>

      {/* Example 6: Login Form */}
      <section className="border rounded-lg p-4">
        <h2 className="text-xl font-semibold mb-4">6. Login Mutation</h2>
        <form onSubmit={handleLogin} className="space-y-4">
          <input
            type="email"
            placeholder="Email"
            value={loginCredentials.email}
            onChange={(e) =>
              setLoginCredentials({ ...loginCredentials, email: e.target.value })
            }
            className="w-full p-2 border rounded"
          />
          <input
            type="password"
            placeholder="Password"
            value={loginCredentials.password}
            onChange={(e) =>
              setLoginCredentials({ ...loginCredentials, password: e.target.value })
            }
            className="w-full p-2 border rounded"
          />
          <button
            type="submit"
            disabled={loginMutation.isPending}
            className="px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50"
          >
            {loginMutation.isPending ? 'Logging in...' : 'Login'}
          </button>
        </form>
      </section>

      {/* API Status */}
      <section className="border rounded-lg p-4 bg-gray-50">
        <h2 className="text-xl font-semibold mb-4">API Status</h2>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <strong>Manga List:</strong>{' '}
            {mangaListLoading ? 'Loading' : mangaListError ? 'Error' : 'Ready'}
          </div>
          <div>
            <strong>Search:</strong>{' '}
            {searchLoading ? 'Loading' : 'Ready'}
          </div>
          <div>
            <strong>Categories:</strong>{' '}
            {categoriesLoading ? 'Loading' : 'Ready'}
          </div>
          <div>
            <strong>Login:</strong>{' '}
            {loginMutation.isPending ? 'Processing' : 'Ready'}
          </div>
        </div>
      </section>
    </div>
  );
};

export default ApiServiceExample;
