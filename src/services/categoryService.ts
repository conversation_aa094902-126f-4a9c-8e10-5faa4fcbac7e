import { apiClient } from './api';
import type { 
  OTruyenApiResponse
} from '../types';

/**
 * Category/Genre interface for OTruyenAPI
 */
export interface Category {
  id: string;
  name: string;
  slug: string;
  description?: string;
  count?: number;
}

/**
 * Category Service - Handles all category/genre-related API calls
 * Provides a clean interface for category operations with the OTruyenAPI
 */
export class CategoryService {
  
  /**
   * Get all available categories/genres
   */
  async getCategories(): Promise<OTruyenApiResponse<Category[]>> {
    return apiClient.getTheLoai();
  }

  /**
   * Get manga by category
   */
  async getMangaByCategory(categorySlug: string, page: number = 1): Promise<OTruyenApiResponse<any>> {
    return apiClient.getTruyenTheoTheLoai(categorySlug, page);
  }

  /**
   * Get category details by slug
   */
  async getCategoryDetails(categorySlug: string): Promise<OTruyenApiResponse<Category>> {
    try {
      const categoriesResponse = await this.getCategories();
      
      if (categoriesResponse.data && Array.isArray(categoriesResponse.data)) {
        const category = categoriesResponse.data.find(cat => cat.slug === categorySlug);
        
        if (category) {
          return { status: true, data: category };
        }
      }
      
      return { status: false, message: 'Category not found' };
    } catch (error) {
      return { status: false, message: 'Failed to get category details' };
    }
  }

  /**
   * Get popular categories (categories with most manga)
   */
  async getPopularCategories(limit: number = 10): Promise<OTruyenApiResponse<Category[]>> {
    try {
      const categoriesResponse = await this.getCategories();
      
      if (categoriesResponse.data && Array.isArray(categoriesResponse.data)) {
        // Sort by count if available, otherwise return first N categories
        const sortedCategories = categoriesResponse.data
          .sort((a, b) => (b.count || 0) - (a.count || 0))
          .slice(0, limit);
        
        return { status: true, data: sortedCategories };
      }
      
      return { status: false, message: 'No categories found' };
    } catch (error) {
      return { status: false, message: 'Failed to get popular categories' };
    }
  }

  /**
   * Search categories by name
   */
  async searchCategories(query: string): Promise<OTruyenApiResponse<Category[]>> {
    try {
      const categoriesResponse = await this.getCategories();
      
      if (categoriesResponse.data && Array.isArray(categoriesResponse.data)) {
        const filteredCategories = categoriesResponse.data.filter(category =>
          category.name.toLowerCase().includes(query.toLowerCase()) ||
          category.slug.toLowerCase().includes(query.toLowerCase())
        );
        
        return { status: true, data: filteredCategories };
      }
      
      return { status: false, message: 'No categories found' };
    } catch (error) {
      return { status: false, message: 'Failed to search categories' };
    }
  }

  /**
   * Get category statistics
   */
  async getCategoryStats(): Promise<OTruyenApiResponse<{
    totalCategories: number;
    categoriesWithManga: number;
    averageMangaPerCategory: number;
  }>> {
    try {
      const categoriesResponse = await this.getCategories();
      
      if (categoriesResponse.data && Array.isArray(categoriesResponse.data)) {
        const categories = categoriesResponse.data;
        const totalCategories = categories.length;
        const categoriesWithManga = categories.filter(cat => (cat.count || 0) > 0).length;
        const totalManga = categories.reduce((sum, cat) => sum + (cat.count || 0), 0);
        const averageMangaPerCategory = totalCategories > 0 ? totalManga / totalCategories : 0;
        
        return {
          status: true,
          data: {
            totalCategories,
            categoriesWithManga,
            averageMangaPerCategory: Math.round(averageMangaPerCategory * 100) / 100,
          }
        };
      }
      
      return { status: false, message: 'No categories found' };
    } catch (error) {
      return { status: false, message: 'Failed to get category statistics' };
    }
  }

  /**
   * Get related categories based on a category
   */
  async getRelatedCategories(categorySlug: string, limit: number = 5): Promise<OTruyenApiResponse<Category[]>> {
    try {
      const categoriesResponse = await this.getCategories();
      
      if (categoriesResponse.data && Array.isArray(categoriesResponse.data)) {
        const currentCategory = categoriesResponse.data.find(cat => cat.slug === categorySlug);
        
        if (!currentCategory) {
          return { status: false, message: 'Category not found' };
        }
        
        // Simple related logic: return other popular categories
        // This can be enhanced with more sophisticated similarity algorithms
        const relatedCategories = categoriesResponse.data
          .filter(cat => cat.slug !== categorySlug)
          .sort((a, b) => (b.count || 0) - (a.count || 0))
          .slice(0, limit);
        
        return { status: true, data: relatedCategories };
      }
      
      return { status: false, message: 'No categories found' };
    } catch (error) {
      return { status: false, message: 'Failed to get related categories' };
    }
  }

  /**
   * Get category hierarchy (if supported by API)
   */
  async getCategoryHierarchy(): Promise<OTruyenApiResponse<any>> {
    // This would depend on the actual API structure
    // For now, return flat categories
    return this.getCategories();
  }

  /**
   * Get trending categories (categories with recent activity)
   */
  async getTrendingCategories(limit: number = 10): Promise<OTruyenApiResponse<Category[]>> {
    // For now, return popular categories
    // This can be enhanced with actual trending logic if API supports it
    return this.getPopularCategories(limit);
  }
}

// Export singleton instance
export const categoryService = new CategoryService();

// Export individual methods for direct use
export const {
  getCategories,
  getMangaByCategory,
  getCategoryDetails,
  getPopularCategories,
  searchCategories,
  getCategoryStats,
  getRelatedCategories,
  getCategoryHierarchy,
  getTrendingCategories,
} = categoryService;
