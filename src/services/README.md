# BlogTruyen API Service Layer

This directory contains the comprehensive API service layer for the BlogTruyen manga reading website, built with TypeScript and integrated with TanStack Query for optimal data fetching and caching.

## Overview

The API service layer provides a clean, type-safe interface for interacting with the OTruyenAPI (https://otruyenapi.com/v1/api) and includes:

- **Core API Client** with comprehensive error handling and interceptors
- **Service Modules** for different API domains (manga, chapters, categories, users)
- **TanStack Query Hooks** for seamless React integration
- **TypeScript Support** with full type safety
- **Caching Strategy** with intelligent cache invalidation
- **Error Handling** with automatic retry and user-friendly error messages

## Architecture

```
src/services/
├── api.ts              # Core API client with sendRequest function
├── mangaService.ts     # Manga-related API operations
├── chapterService.ts   # Chapter-related API operations
├── categoryService.ts  # Category/genre API operations
├── userService.ts      # User authentication and profile operations
├── queryClient.ts      # TanStack Query configuration
├── index.ts           # Central export point
└── README.md          # This documentation
```

## Core Features

### 1. Generic sendRequest Function

The core `sendRequest` function provides:

```typescript
async sendRequest<TResponse = any, TData = any>(
  endpoint: string,
  method: HttpMethod = 'GET',
  data?: TData,
  config: Partial<RequestConfig> = {}
): Promise<TResponse>
```

**Features:**
- Support for all HTTP methods (GET, POST, PUT, DELETE, PATCH, HEAD, OPTIONS)
- Automatic request/response type safety with TypeScript generics
- Request/response interceptors for common headers and data transformation
- Comprehensive error handling with custom ApiError types
- Automatic timeout handling (30 seconds default)
- Request retry with exponential backoff
- Authentication token management

### 2. Service Modules

#### MangaService (`mangaService.ts`)
Handles all manga-related operations:
- `getMangaList()` - Get paginated manga list with filters
- `getRecentlyUpdated()` - Get recently updated manga
- `getCompleted()` - Get completed manga
- `getMangaByGenre()` - Get manga by category/genre
- `searchManga()` - Search manga by keyword
- `getMangaDetails()` - Get detailed manga information
- `getMangaChapters()` - Get chapter list for a manga
- `getTopManga()` - Get trending/popular manga

#### ChapterService (`chapterService.ts`)
Handles chapter-related operations:
- `getChapterContent()` - Get chapter pages and content
- `getChapterNavigation()` - Get previous/next chapter info
- `getChapterImages()` - Extract chapter image URLs
- `preloadNextChapterImages()` - Preload images for better UX

#### CategoryService (`categoryService.ts`)
Handles category/genre operations:
- `getCategories()` - Get all available categories
- `getMangaByCategory()` - Get manga filtered by category
- `getPopularCategories()` - Get most popular categories
- `searchCategories()` - Search categories by name

#### UserService (`userService.ts`)
Handles user authentication and profile:
- `login()` / `register()` / `logout()` - Authentication
- `getProfile()` / `updateProfile()` - User profile management
- `getBookmarks()` / `addBookmark()` / `removeBookmark()` - Bookmark management
- `getReadingHistory()` / `updateReadingProgress()` - Reading progress tracking

### 3. TanStack Query Integration

#### Query Hooks (`useApiQueries.ts`)
Pre-configured hooks with optimal caching strategies:

```typescript
// Manga queries
useMangaListQuery(filters)
useMangaInfiniteQuery(filters)  // For infinite scrolling
useMangaDetailQuery(slug)
useMangaChaptersQuery(slug)
usePopularMangaQuery(page)
useTrendingMangaQuery(page)
useSearchMangaQuery(query, page)

// Chapter queries
useChapterContentQuery(mangaSlug, chapterSlug)
useChapterNavigationQuery(mangaSlug, chapterSlug)

// Category queries
useCategoriesQuery()
usePopularCategoriesQuery(limit)

// User queries
useUserProfileQuery()
useUserBookmarksQuery()
useUserHistoryQuery()
```

#### Mutation Hooks
For data modification operations:

```typescript
// Authentication
useLoginMutation()
useRegisterMutation()
useLogoutMutation()

// User actions
useBookmarkMutation()  // Returns { addBookmark, removeBookmark, toggleBookmark }
useReadingProgressMutation()
useUpdateProfileMutation()
```

### 4. Error Handling

The API service includes comprehensive error handling:

```typescript
interface ApiError {
  status: number;
  statusText: string;
  message: string;
  data?: any;
  url?: string;
  method?: HttpMethod;
}
```

**Error Interceptors:**
- 401: Automatic token cleanup and redirect to login
- 403: Access denied warnings
- 404: Resource not found handling
- 429: Rate limiting notifications
- 5xx: Server error handling

### 5. Caching Strategy

**Query Stale Times:**
- Manga lists: 5 minutes
- Manga details: 10 minutes
- Chapters: 30 minutes (rarely change)
- Categories: 1 hour (very stable)
- User data: 5-10 minutes

**Cache Invalidation:**
- Automatic invalidation on mutations
- Smart invalidation based on data relationships
- Manual cache clearing on logout

## Usage Examples

### Basic Manga List
```typescript
import { useMangaListQuery } from '@/hooks/useManga';

function MangaList() {
  const { data, isLoading, error } = useMangaListQuery({
    page: 1,
    status: 'ongoing'
  });

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div>
      {data?.data?.items?.map(manga => (
        <div key={manga.id}>{manga.title}</div>
      ))}
    </div>
  );
}
```

### Infinite Scrolling
```typescript
import { useMangaInfiniteQuery } from '@/hooks/useManga';

function InfiniteMangaList() {
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useMangaInfiniteQuery({ status: 'ongoing' });

  return (
    <div>
      {data?.pages.map((page, i) => (
        <div key={i}>
          {page.data?.items?.map(manga => (
            <div key={manga.id}>{manga.title}</div>
          ))}
        </div>
      ))}
      
      {hasNextPage && (
        <button 
          onClick={() => fetchNextPage()}
          disabled={isFetchingNextPage}
        >
          {isFetchingNextPage ? 'Loading...' : 'Load More'}
        </button>
      )}
    </div>
  );
}
```

### User Authentication
```typescript
import { useLoginMutation } from '@/hooks/useApiQueries';

function LoginForm() {
  const loginMutation = useLoginMutation({
    onSuccess: (data) => {
      console.log('Login successful:', data);
      // Redirect to dashboard
    },
    onError: (error) => {
      console.error('Login failed:', error.message);
    }
  });

  const handleSubmit = (credentials) => {
    loginMutation.mutate(credentials);
  };

  return (
    <form onSubmit={handleSubmit}>
      {/* Form fields */}
      <button 
        type="submit" 
        disabled={loginMutation.isPending}
      >
        {loginMutation.isPending ? 'Logging in...' : 'Login'}
      </button>
    </form>
  );
}
```

## Configuration

### Environment Variables
```env
VITE_API_BASE_URL=https://otruyenapi.com/v1/api  # Optional override
```

### Query Client Configuration
The query client is pre-configured with optimal defaults:
- 5-minute stale time for most queries
- 10-minute garbage collection time
- Smart retry logic (no retry on 4xx errors)
- Disabled refetch on window focus

## Best Practices

1. **Use the appropriate hook** for your use case (query vs infinite query vs mutation)
2. **Handle loading and error states** in your components
3. **Leverage the caching** - avoid unnecessary API calls
4. **Use TypeScript** for better development experience
5. **Handle offline scenarios** with proper error boundaries
6. **Implement optimistic updates** for better UX where appropriate

## Extending the API Service

To add new endpoints:

1. Add the endpoint method to the appropriate service class
2. Create corresponding TanStack Query hooks in `useApiQueries.ts`
3. Add proper TypeScript types in `src/types/index.ts`
4. Update the service index exports
5. Add tests for the new functionality

## Testing

The API service is designed to be easily testable:
- Mock the service classes for unit tests
- Use MSW (Mock Service Worker) for integration tests
- TanStack Query provides excellent testing utilities

For more information, see the individual service files and the TanStack Query documentation.
