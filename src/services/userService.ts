import { apiClient } from './api';
import type { 
  User, 
  LoginCredentials, 
  RegisterData, 
  Manga,
  ApiResponse,
  ReadingHistory
} from '../types';

/**
 * User Service - Handles all user-related API calls
 * Provides a clean interface for user operations
 */
export class UserService {
  
  /**
   * Authenticate user with credentials
   */
  async login(credentials: LoginCredentials): Promise<ApiResponse<{ user: User; token: string }>> {
    const response = await apiClient.login(credentials);
    
    // Store auth token if login successful
    if (response.success && response.data.token) {
      localStorage.setItem('auth-token', response.data.token);
      localStorage.setItem('user', JSON.stringify(response.data.user));
    }
    
    return response;
  }

  /**
   * Register new user
   */
  async register(data: RegisterData): Promise<ApiResponse<{ user: User; token: string }>> {
    const response = await apiClient.register(data);
    
    // Store auth token if registration successful
    if (response.success && response.data.token) {
      localStorage.setItem('auth-token', response.data.token);
      localStorage.setItem('user', JSON.stringify(response.data.user));
    }
    
    return response;
  }

  /**
   * Logout user
   */
  async logout(): Promise<ApiResponse<null>> {
    try {
      const response = await apiClient.logout();
      
      // Clear local storage regardless of API response
      localStorage.removeItem('auth-token');
      localStorage.removeItem('user');
      
      return response;
    } catch (error) {
      // Clear local storage even if API call fails
      localStorage.removeItem('auth-token');
      localStorage.removeItem('user');
      
      // Return success since local logout is complete
      return { success: true, data: null };
    }
  }

  /**
   * Get current user profile
   */
  async getProfile(): Promise<ApiResponse<User>> {
    return apiClient.getUserProfile();
  }

  /**
   * Update user profile
   */
  async updateProfile(data: Partial<User>): Promise<ApiResponse<User>> {
    const response = await apiClient.updateUserProfile(data);
    
    // Update local user data if successful
    if (response.success && response.data) {
      localStorage.setItem('user', JSON.stringify(response.data));
    }
    
    return response;
  }

  /**
   * Get user bookmarks
   */
  async getBookmarks(): Promise<ApiResponse<Manga[]>> {
    return apiClient.getUserBookmarks();
  }

  /**
   * Add manga to bookmarks
   */
  async addBookmark(mangaId: string): Promise<ApiResponse<null>> {
    return apiClient.addBookmark(mangaId);
  }

  /**
   * Remove manga from bookmarks
   */
  async removeBookmark(mangaId: string): Promise<ApiResponse<null>> {
    return apiClient.removeBookmark(mangaId);
  }

  /**
   * Toggle bookmark status
   */
  async toggleBookmark(mangaId: string, isBookmarked: boolean): Promise<ApiResponse<null>> {
    if (isBookmarked) {
      return this.removeBookmark(mangaId);
    } else {
      return this.addBookmark(mangaId);
    }
  }

  /**
   * Check if manga is bookmarked
   */
  async isBookmarked(mangaId: string): Promise<boolean> {
    try {
      const response = await this.getBookmarks();
      
      if (response.success && response.data) {
        return response.data.some(manga => manga.id === mangaId);
      }
      
      return false;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get reading history
   */
  async getReadingHistory(): Promise<ApiResponse<ReadingHistory[]>> {
    return apiClient.getReadingHistory();
  }

  /**
   * Update reading progress
   */
  async updateReadingProgress(
    mangaId: string, 
    chapterId: string, 
    pageNumber: number
  ): Promise<ApiResponse<null>> {
    return apiClient.updateReadingProgress(mangaId, chapterId, pageNumber);
  }

  /**
   * Get current user from local storage
   */
  getCurrentUser(): User | null {
    try {
      const userStr = localStorage.getItem('user');
      return userStr ? JSON.parse(userStr) : null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Get auth token from local storage
   */
  getAuthToken(): string | null {
    return localStorage.getItem('auth-token');
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    return !!this.getAuthToken();
  }

  /**
   * Clear all user data (for logout)
   */
  clearUserData(): void {
    localStorage.removeItem('auth-token');
    localStorage.removeItem('user');
  }

  /**
   * Get user preferences (stored locally)
   */
  getUserPreferences(): any {
    try {
      const prefsStr = localStorage.getItem('user-preferences');
      return prefsStr ? JSON.parse(prefsStr) : {};
    } catch (error) {
      return {};
    }
  }

  /**
   * Update user preferences (stored locally)
   */
  updateUserPreferences(preferences: any): void {
    try {
      const currentPrefs = this.getUserPreferences();
      const updatedPrefs = { ...currentPrefs, ...preferences };
      localStorage.setItem('user-preferences', JSON.stringify(updatedPrefs));
    } catch (error) {
      console.error('Failed to update user preferences:', error);
    }
  }

  /**
   * Get reading settings (stored locally)
   */
  getReadingSettings(): any {
    try {
      const settingsStr = localStorage.getItem('reading-settings');
      return settingsStr ? JSON.parse(settingsStr) : {
        readingMode: 'single',
        readingDirection: 'ltr',
        pageFit: 'width',
        brightness: 100,
        backgroundColor: '#ffffff'
      };
    } catch (error) {
      return {
        readingMode: 'single',
        readingDirection: 'ltr',
        pageFit: 'width',
        brightness: 100,
        backgroundColor: '#ffffff'
      };
    }
  }

  /**
   * Update reading settings (stored locally)
   */
  updateReadingSettings(settings: any): void {
    try {
      const currentSettings = this.getReadingSettings();
      const updatedSettings = { ...currentSettings, ...settings };
      localStorage.setItem('reading-settings', JSON.stringify(updatedSettings));
    } catch (error) {
      console.error('Failed to update reading settings:', error);
    }
  }
}

// Export singleton instance
export const userService = new UserService();

// Export individual methods for direct use
export const {
  login,
  register,
  logout,
  getProfile,
  updateProfile,
  getBookmarks,
  addBookmark,
  removeBookmark,
  toggleBookmark,
  isBookmarked,
  getReadingHistory,
  updateReadingProgress,
  getCurrentUser,
  getAuthToken,
  isAuthenticated,
  clearUserData,
  getUserPreferences,
  updateUserPreferences,
  getReadingSettings,
  updateReadingSettings,
} = userService;
