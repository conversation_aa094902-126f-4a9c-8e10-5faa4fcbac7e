import { apiClient } from './api';
import type { 
  Chapter, 
  ApiResponse,
  OTruyenApiResponse
} from '../types';

/**
 * Chapter Service - Handles all chapter-related API calls
 * Provides a clean interface for chapter operations with the OTruyenAPI
 */
export class ChapterService {
  
  /**
   * Get chapter content by manga slug and chapter slug
   */
  async getChapterContent(mangaSlug: string, chapterSlug: string): Promise<OTruyenApiResponse<Chapter>> {
    return apiClient.getChapterContent(mangaSlug, chapterSlug);
  }

  /**
   * Get chapter details with navigation info
   */
  async getChapterDetails(mangaSlug: string, chapterSlug: string): Promise<OTruyenApiResponse<any>> {
    const response = await this.getChapterContent(mangaSlug, chapterSlug);
    
    // Add navigation helpers if needed
    if (response.data) {
      // You can add logic here to determine next/previous chapters
      // This would require additional API calls or data processing
    }
    
    return response;
  }

  /**
   * Get chapter list for a manga
   */
  async getChapterList(mangaSlug: string): Promise<OTruyenApiResponse<Chapter[]>> {
    return apiClient.getChapterList(mangaSlug);
  }

  /**
   * Get next chapter information
   */
  async getNextChapter(mangaSlug: string, currentChapterSlug: string): Promise<OTruyenApiResponse<Chapter | null>> {
    try {
      const chaptersResponse = await this.getChapterList(mangaSlug);
      
      if (!chaptersResponse.data || !Array.isArray(chaptersResponse.data)) {
        return { status: false, message: 'No chapters found' };
      }
      
      const chapters = chaptersResponse.data;
      const currentIndex = chapters.findIndex(chapter => 
        chapter.id === currentChapterSlug || 
        chapter.title?.includes(currentChapterSlug)
      );
      
      if (currentIndex !== -1 && currentIndex < chapters.length - 1) {
        const nextChapter = chapters[currentIndex + 1];
        return { status: true, data: nextChapter };
      }
      
      return { status: false, message: 'No next chapter available' };
    } catch (error) {
      return { status: false, message: 'Failed to get next chapter' };
    }
  }

  /**
   * Get previous chapter information
   */
  async getPreviousChapter(mangaSlug: string, currentChapterSlug: string): Promise<OTruyenApiResponse<Chapter | null>> {
    try {
      const chaptersResponse = await this.getChapterList(mangaSlug);
      
      if (!chaptersResponse.data || !Array.isArray(chaptersResponse.data)) {
        return { status: false, message: 'No chapters found' };
      }
      
      const chapters = chaptersResponse.data;
      const currentIndex = chapters.findIndex(chapter => 
        chapter.id === currentChapterSlug || 
        chapter.title?.includes(currentChapterSlug)
      );
      
      if (currentIndex > 0) {
        const previousChapter = chapters[currentIndex - 1];
        return { status: true, data: previousChapter };
      }
      
      return { status: false, message: 'No previous chapter available' };
    } catch (error) {
      return { status: false, message: 'Failed to get previous chapter' };
    }
  }

  /**
   * Get chapter navigation info (previous, current, next)
   */
  async getChapterNavigation(mangaSlug: string, currentChapterSlug: string): Promise<{
    previous: Chapter | null;
    current: Chapter | null;
    next: Chapter | null;
  }> {
    try {
      const [chaptersResponse, currentResponse] = await Promise.all([
        this.getChapterList(mangaSlug),
        this.getChapterContent(mangaSlug, currentChapterSlug)
      ]);
      
      const chapters = chaptersResponse.data || [];
      const current = currentResponse.data || null;
      
      const currentIndex = chapters.findIndex(chapter => 
        chapter.id === currentChapterSlug || 
        chapter.title?.includes(currentChapterSlug)
      );
      
      return {
        previous: currentIndex > 0 ? chapters[currentIndex - 1] : null,
        current,
        next: currentIndex !== -1 && currentIndex < chapters.length - 1 ? chapters[currentIndex + 1] : null,
      };
    } catch (error) {
      return {
        previous: null,
        current: null,
        next: null,
      };
    }
  }

  /**
   * Get chapter images/pages
   */
  async getChapterImages(mangaSlug: string, chapterSlug: string): Promise<string[]> {
    try {
      const response = await this.getChapterContent(mangaSlug, chapterSlug);
      
      if (response.data && response.data.pages) {
        return response.data.pages;
      }
      
      // If pages are in a different structure, adapt accordingly
      if (response.data && Array.isArray(response.data)) {
        return response.data;
      }
      
      return [];
    } catch (error) {
      console.error('Failed to get chapter images:', error);
      return [];
    }
  }

  /**
   * Preload next chapter images for better reading experience
   */
  async preloadNextChapterImages(mangaSlug: string, currentChapterSlug: string): Promise<void> {
    try {
      const nextChapterResponse = await this.getNextChapter(mangaSlug, currentChapterSlug);
      
      if (nextChapterResponse.data) {
        const nextChapterImages = await this.getChapterImages(mangaSlug, nextChapterResponse.data.id);
        
        // Preload first few images of next chapter
        const imagesToPreload = nextChapterImages.slice(0, 3);
        imagesToPreload.forEach(imageUrl => {
          const img = new Image();
          img.src = imageUrl;
        });
      }
    } catch (error) {
      // Silently fail preloading
      console.warn('Failed to preload next chapter images:', error);
    }
  }
}

// Export singleton instance
export const chapterService = new ChapterService();

// Export individual methods for direct use
export const {
  getChapterContent,
  getChapterDetails,
  getChapterList,
  getNextChapter,
  getPreviousChapter,
  getChapterNavigation,
  getChapterImages,
  preloadNextChapterImages,
} = chapterService;
