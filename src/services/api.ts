import type {
  Manga,
  Chapter,
  User,
  LoginCredentials,
  RegisterData,
  MangaFilters,
  PaginatedResponse,
  ApiResponse,
  ApiError,
  RequestConfig,
  HttpMethod,
} from "../types";

// Base API configuration
const API_BASE_URL = "https://otruyenapi.com/v1/api";
const REQUEST_TIMEOUT = 30000; // 30 seconds

// Request interceptor type
type RequestInterceptor = (
  config: RequestConfig
) => RequestConfig | Promise<RequestConfig>;
type ResponseInterceptor<T = any> = (response: T) => T | Promise<T>;
type ErrorInterceptor = (error: ApiError) => Promise<never>;

class ApiClient {
  private baseURL: string;
  private defaultHeaders: Record<string, string>;
  private requestInterceptors: RequestInterceptor[] = [];
  private responseInterceptors: ResponseInterceptor[] = [];
  private errorInterceptors: ErrorInterceptor[] = [];

  constructor(baseURL: string) {
    this.baseURL = baseURL;
    this.defaultHeaders = {
      "Content-Type": "application/json",
      Accept: "application/json",
    };
  }

  // Interceptor management
  addRequestInterceptor(interceptor: RequestInterceptor): void {
    this.requestInterceptors.push(interceptor);
  }

  addResponseInterceptor(interceptor: ResponseInterceptor): void {
    this.responseInterceptors.push(interceptor);
  }

  addErrorInterceptor(interceptor: ErrorInterceptor): void {
    this.errorInterceptors.push(interceptor);
  }

  // Core request method with comprehensive error handling and interceptors
  async sendRequest<TResponse = any, TData = any>(
    endpoint: string,
    method: HttpMethod = "GET",
    data?: TData,
    config: Partial<RequestConfig> = {}
  ): Promise<TResponse> {
    // Build initial request configuration
    let requestConfig: RequestConfig = {
      method,
      url: `${this.baseURL}${endpoint}`,
      headers: {
        ...this.defaultHeaders,
        ...config.headers,
      },
      timeout: config.timeout || REQUEST_TIMEOUT,
      ...config,
    };

    // Add auth token if available
    const token = localStorage.getItem("auth-token");
    if (token && !requestConfig.headers?.Authorization) {
      requestConfig.headers = {
        ...requestConfig.headers,
        Authorization: `Bearer ${token}`,
      };
    }

    // Apply request interceptors
    for (const interceptor of this.requestInterceptors) {
      requestConfig = await interceptor(requestConfig);
    }

    // Build fetch options
    const fetchOptions: RequestInit = {
      method: requestConfig.method,
      headers: requestConfig.headers,
      signal: AbortSignal.timeout(requestConfig.timeout),
    };

    // Add body for non-GET requests
    if (data && !["GET", "HEAD"].includes(method)) {
      if (
        requestConfig.headers?.["Content-Type"]?.includes("application/json")
      ) {
        fetchOptions.body = JSON.stringify(data);
      } else {
        fetchOptions.body = data as BodyInit;
      }
    }

    try {
      const response = await fetch(requestConfig.url, fetchOptions);

      // Handle HTTP errors
      if (!response.ok) {
        const errorData = await this.parseErrorResponse(response);
        const apiError: ApiError = {
          status: response.status,
          statusText: response.statusText,
          message:
            errorData.message ||
            `HTTP ${response.status}: ${response.statusText}`,
          data: errorData,
          url: requestConfig.url,
          method: requestConfig.method,
        };

        // Apply error interceptors
        for (const interceptor of this.errorInterceptors) {
          await interceptor(apiError);
        }

        throw apiError;
      }

      // Parse response
      let responseData: TResponse;
      const contentType = response.headers.get("content-type");

      if (contentType?.includes("application/json")) {
        responseData = await response.json();
      } else {
        responseData = (await response.text()) as unknown as TResponse;
      }

      // Apply response interceptors
      for (const interceptor of this.responseInterceptors) {
        responseData = await interceptor(responseData);
      }

      return responseData;
    } catch (error) {
      // Handle network errors and other exceptions
      if (error instanceof Error && error.name === "AbortError") {
        const timeoutError: ApiError = {
          status: 408,
          statusText: "Request Timeout",
          message: `Request timed out after ${requestConfig.timeout}ms`,
          url: requestConfig.url,
          method: requestConfig.method,
        };
        throw timeoutError;
      }

      // Re-throw ApiError instances
      if (this.isApiError(error)) {
        throw error;
      }

      // Wrap other errors
      const networkError: ApiError = {
        status: 0,
        statusText: "Network Error",
        message:
          error instanceof Error ? error.message : "Unknown network error",
        url: requestConfig.url,
        method: requestConfig.method,
      };

      throw networkError;
    }
  }

  // Helper method to parse error responses
  private async parseErrorResponse(response: Response): Promise<any> {
    try {
      const contentType = response.headers.get("content-type");
      if (contentType?.includes("application/json")) {
        return await response.json();
      } else {
        const text = await response.text();
        return { message: text };
      }
    } catch {
      return { message: "Failed to parse error response" };
    }
  }

  // Type guard for ApiError
  private isApiError(error: any): error is ApiError {
    return (
      error &&
      typeof error.status === "number" &&
      typeof error.message === "string"
    );
  }

  // OTruyenAPI specific endpoints

  // Get danh sách truyện tranh (manga list)
  async getTruyenList(page: number = 1): Promise<any> {
    return this.sendRequest(`/danh-sach/truyen-tranh?page=${page}`);
  }

  // Get truyện tranh mới cập nhật (recently updated manga)
  async getTruyenMoiCapNhat(page: number = 1): Promise<any> {
    return this.sendRequest(`/danh-sach/truyen-moi?page=${page}`);
  }

  // Get truyện tranh hoàn thành (completed manga)
  async getTruyenHoanThanh(page: number = 1): Promise<any> {
    return this.sendRequest(`/danh-sach/hoan-thanh?page=${page}`);
  }

  // Get thể loại (genres/categories)
  async getTheLoai(): Promise<any> {
    return this.sendRequest("/the-loai");
  }

  // Get truyện theo thể loại (manga by genre)
  async getTruyenTheoTheLoai(slug: string, page: number = 1): Promise<any> {
    return this.sendRequest(`/the-loai/${slug}?page=${page}`);
  }

  // Get chi tiết truyện (manga details)
  async getTruyenDetail(slug: string): Promise<any> {
    return this.sendRequest(`/truyen-tranh/${slug}`);
  }

  // Get danh sách chương (chapter list)
  async getChapterList(slug: string): Promise<any> {
    return this.sendRequest(`/truyen-tranh/${slug}`);
  }

  // Get nội dung chương (chapter content)
  async getChapterContent(
    truyenSlug: string,
    chapterSlug: string
  ): Promise<any> {
    return this.sendRequest(`/truyen-tranh/${truyenSlug}/${chapterSlug}`);
  }

  // Search truyện tranh (search manga)
  async searchTruyen(keyword: string, page: number = 1): Promise<any> {
    return this.sendRequest(
      `/tim-kiem?keyword=${encodeURIComponent(keyword)}&page=${page}`
    );
  }

  // Get top truyện (top manga)
  async getTopTruyen(
    type: "daily" | "weekly" | "monthly" = "daily",
    page: number = 1
  ): Promise<any> {
    return this.sendRequest(`/top/${type}?page=${page}`);
  }

  // Legacy Auth endpoints (for future backend integration)
  async login(
    credentials: LoginCredentials
  ): Promise<ApiResponse<{ user: User; token: string }>> {
    return this.sendRequest("/auth/login", "POST", credentials);
  }

  async register(
    data: RegisterData
  ): Promise<ApiResponse<{ user: User; token: string }>> {
    return this.sendRequest("/auth/register", "POST", data);
  }

  async logout(): Promise<ApiResponse<null>> {
    return this.sendRequest("/auth/logout", "POST");
  }

  // Wrapper methods for backward compatibility with existing hooks
  async getMangaList(
    filters: MangaFilters = {}
  ): Promise<PaginatedResponse<Manga>> {
    const page = filters.page || 1;

    if (filters.search) {
      return this.searchTruyen(filters.search, page);
    }

    if (filters.status === "completed") {
      return this.getTruyenHoanThanh(page);
    }

    return this.getTruyenList(page);
  }

  async getMangaById(id: string): Promise<ApiResponse<Manga>> {
    return this.getTruyenDetail(id);
  }

  async getMangaChapters(mangaId: string): Promise<ApiResponse<Chapter[]>> {
    return this.getChapterList(mangaId);
  }

  // Chapter endpoints
  async getChapterById(
    mangaSlug: string,
    chapterSlug: string
  ): Promise<ApiResponse<Chapter>> {
    return this.getChapterContent(mangaSlug, chapterSlug);
  }

  // User endpoints
  async getUserProfile(): Promise<ApiResponse<User>> {
    return this.sendRequest("/user/profile");
  }

  async updateUserProfile(data: Partial<User>): Promise<ApiResponse<User>> {
    return this.sendRequest("/user/profile", "PUT", data);
  }

  // Bookmarks
  async getUserBookmarks(): Promise<ApiResponse<Manga[]>> {
    return this.sendRequest("/user/bookmarks");
  }

  async addBookmark(mangaId: string): Promise<ApiResponse<null>> {
    return this.sendRequest("/user/bookmarks", "POST", { mangaId });
  }

  async removeBookmark(mangaId: string): Promise<ApiResponse<null>> {
    return this.sendRequest(`/user/bookmarks/${mangaId}`, "DELETE");
  }

  // Reading history
  async getReadingHistory(): Promise<ApiResponse<any[]>> {
    return this.sendRequest("/user/history");
  }

  async updateReadingProgress(
    mangaId: string,
    chapterId: string,
    pageNumber: number
  ): Promise<ApiResponse<null>> {
    return this.sendRequest("/user/history", "POST", {
      mangaId,
      chapterId,
      pageNumber,
    });
  }
}

// Create and configure the API client instance
export const apiClient = new ApiClient(API_BASE_URL);

// Add default request interceptor for logging and common headers
apiClient.addRequestInterceptor((config) => {
  // Log requests in development
  if (import.meta.env.DEV) {
    console.log(`🚀 API Request: ${config.method} ${config.url}`);
  }

  // Add common headers
  config.headers = {
    ...config.headers,
    "X-Requested-With": "XMLHttpRequest",
    "Cache-Control": "no-cache",
  };

  return config;
});

// Add default response interceptor for logging and data transformation
apiClient.addResponseInterceptor((response) => {
  // Log responses in development
  if (import.meta.env.DEV) {
    console.log("✅ API Response:", response);
  }

  return response;
});

// Add default error interceptor for common error handling
apiClient.addErrorInterceptor(async (error) => {
  // Log errors in development
  if (import.meta.env.DEV) {
    console.error("❌ API Error:", error);
  }

  // Handle common error scenarios
  switch (error.status) {
    case 401:
      // Unauthorized - clear auth token and redirect to login
      localStorage.removeItem("auth-token");
      if (
        typeof window !== "undefined" &&
        window.location.pathname !== "/login"
      ) {
        window.location.href = "/login";
      }
      break;
    case 403:
      // Forbidden - show access denied message
      console.warn(
        "Access denied. You do not have permission to perform this action."
      );
      break;
    case 404:
      // Not found - could be handled by individual components
      console.warn("Resource not found.");
      break;
    case 429:
      // Rate limited - show rate limit message
      console.warn("Too many requests. Please try again later.");
      break;
    case 500:
    case 502:
    case 503:
    case 504:
      // Server errors - show generic server error message
      console.error("Server error. Please try again later.");
      break;
  }

  // Re-throw the error for component-level handling
  throw error;
});

// Export additional utility functions
export const createApiError = (
  status: number,
  message: string,
  data?: any
): ApiError => ({
  status,
  statusText: "",
  message,
  data,
});

export const isApiError = (error: any): error is ApiError => {
  return (
    error &&
    typeof error.status === "number" &&
    typeof error.message === "string"
  );
};
