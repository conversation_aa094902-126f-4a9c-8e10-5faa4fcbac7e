import { apiClient } from './api';
import type { 
  Manga, 
  Chapter, 
  MangaFilters, 
  PaginatedResponse, 
  ApiResponse,
  OTruyenApiResponse,
  OTruyenPaginatedResponse
} from '../types';

/**
 * Manga Service - Handles all manga-related API calls
 * Provides a clean interface for manga operations with the OTruyenAPI
 */
export class MangaService {
  
  /**
   * Get list of manga with optional filters
   */
  async getMangaList(filters: MangaFilters = {}): Promise<OTruyenPaginatedResponse<Manga>> {
    const page = filters.page || 1;
    
    if (filters.search) {
      return this.searchManga(filters.search, page);
    }
    
    if (filters.status === 'completed') {
      return apiClient.getTruyenHoanThanh(page);
    }
    
    if (filters.genres && filters.genres.length > 0) {
      // For genre filtering, use the first genre
      return apiClient.getTruyenTheoTheLoai(filters.genres[0], page);
    }
    
    return apiClient.getTruyenList(page);
  }

  /**
   * Get recently updated manga
   */
  async getRecentlyUpdated(page: number = 1): Promise<OTruyenApiResponse<any>> {
    return apiClient.getTruyenMoiCapNhat(page);
  }

  /**
   * Get completed manga
   */
  async getCompleted(page: number = 1): Promise<OTruyenApiResponse<any>> {
    return apiClient.getTruyenHoanThanh(page);
  }

  /**
   * Get manga by genre/category
   */
  async getMangaByGenre(genreSlug: string, page: number = 1): Promise<OTruyenApiResponse<any>> {
    return apiClient.getTruyenTheoTheLoai(genreSlug, page);
  }

  /**
   * Search manga by keyword
   */
  async searchManga(keyword: string, page: number = 1): Promise<OTruyenApiResponse<any>> {
    return apiClient.searchTruyen(keyword, page);
  }

  /**
   * Get manga details by slug
   */
  async getMangaDetails(slug: string): Promise<OTruyenApiResponse<Manga>> {
    return apiClient.getTruyenDetail(slug);
  }

  /**
   * Get manga chapters
   */
  async getMangaChapters(slug: string): Promise<OTruyenApiResponse<Chapter[]>> {
    return apiClient.getChapterList(slug);
  }

  /**
   * Get top/trending manga
   */
  async getTopManga(type: 'daily' | 'weekly' | 'monthly' = 'daily', page: number = 1): Promise<OTruyenApiResponse<any>> {
    return apiClient.getTopTruyen(type, page);
  }

  /**
   * Get popular manga (alias for daily top)
   */
  async getPopularManga(page: number = 1): Promise<OTruyenApiResponse<any>> {
    return this.getTopManga('daily', page);
  }

  /**
   * Get trending manga (alias for weekly top)
   */
  async getTrendingManga(page: number = 1): Promise<OTruyenApiResponse<any>> {
    return this.getTopManga('weekly', page);
  }

  /**
   * Get featured manga (alias for monthly top)
   */
  async getFeaturedManga(page: number = 1): Promise<OTruyenApiResponse<any>> {
    return this.getTopManga('monthly', page);
  }

  /**
   * Get manga recommendations based on a manga
   * Note: This might not be available in OTruyenAPI, implement as needed
   */
  async getRecommendations(mangaSlug: string): Promise<OTruyenApiResponse<Manga[]>> {
    // For now, return similar genre manga or popular manga
    // This can be enhanced based on actual API capabilities
    return this.getPopularManga(1);
  }

  /**
   * Get manga by multiple filters with advanced options
   */
  async getAdvancedMangaList(options: {
    genres?: string[];
    status?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    page?: number;
    limit?: number;
  }): Promise<OTruyenApiResponse<any>> {
    const { genres, status, page = 1 } = options;
    
    // Handle different filter combinations
    if (status === 'completed') {
      return this.getCompleted(page);
    }
    
    if (genres && genres.length > 0) {
      return this.getMangaByGenre(genres[0], page);
    }
    
    return apiClient.getTruyenList(page);
  }
}

// Export singleton instance
export const mangaService = new MangaService();

// Export individual methods for direct use
export const {
  getMangaList,
  getRecentlyUpdated,
  getCompleted,
  getMangaByGenre,
  searchManga,
  getMangaDetails,
  getMangaChapters,
  getTopManga,
  getPopularManga,
  getTrendingManga,
  getFeaturedManga,
  getRecommendations,
  getAdvancedMangaList,
} = mangaService;
