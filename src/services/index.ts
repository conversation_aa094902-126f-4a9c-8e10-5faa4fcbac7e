/**
 * Services Index - Central export point for all API services
 * Provides easy access to all service modules and utilities
 */

// Core API client
export { apiClient, createApiError, isApiError } from './api';

// Service modules
export { mangaService } from './mangaService';
export { chapterService } from './chapterService';
export { categoryService } from './categoryService';
export { userService } from './userService';

// Query client
export { queryClient } from './queryClient';

// Individual service methods for direct import
export {
  getMangaList,
  getRecentlyUpdated,
  getCompleted,
  getMangaByGenre,
  searchManga,
  getMangaDetails,
  getMangaChapters,
  getTopManga,
  getPopularManga,
  getTrendingManga,
  getFeaturedManga,
  getRecommendations,
  getAdvancedMangaList,
} from './mangaService';

export {
  getChapterContent,
  getChapterDetails,
  getChapterList,
  getNextChapter,
  getPreviousChapter,
  getChapterNavigation,
  getChapterImages,
  preloadNextChapterImages,
} from './chapterService';

export {
  getCategories,
  getMangaByCategory,
  getCategoryDetails,
  getPopularCategories,
  searchCategories,
  getCategoryStats,
  getRelatedCategories,
  getCategoryHierarchy,
  getTrendingCategories,
} from './categoryService';

export {
  login,
  register,
  logout,
  getProfile,
  updateProfile,
  getBookmarks,
  addBookmark,
  removeBookmark,
  toggleBookmark,
  isBookmarked,
  getReadingHistory,
  updateReadingProgress,
  getCurrentUser,
  getAuthToken,
  isAuthenticated,
  clearUserData,
  getUserPreferences,
  updateUserPreferences,
  getReadingSettings,
  updateReadingSettings,
} from './userService';

// Types
export type {
  ApiError,
  RequestConfig,
  HttpMethod,
  ApiResponse,
  OTruyenApiResponse,
  OTruyenPaginatedResponse,
} from '../types';

// Service configuration
export const API_CONFIG = {
  BASE_URL: 'https://otruyenapi.com/v1/api',
  TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
} as const;

// Service utilities
export const serviceUtils = {
  /**
   * Create a standardized error response
   */
  createErrorResponse: (message: string, status?: number) => ({
    status: false,
    message,
    ...(status && { status }),
  }),

  /**
   * Create a standardized success response
   */
  createSuccessResponse: <T>(data: T, message?: string) => ({
    status: true,
    data,
    ...(message && { message }),
  }),

  /**
   * Check if response is successful
   */
  isSuccessResponse: (response: any): boolean => {
    return response && response.status === true;
  },

  /**
   * Extract data from response safely
   */
  extractData: <T>(response: any, fallback?: T): T | undefined => {
    if (response && response.status === true && response.data) {
      return response.data;
    }
    return fallback;
  },

  /**
   * Format API endpoint with parameters
   */
  formatEndpoint: (endpoint: string, params: Record<string, any> = {}): string => {
    let formattedEndpoint = endpoint;
    
    // Replace path parameters
    Object.entries(params).forEach(([key, value]) => {
      formattedEndpoint = formattedEndpoint.replace(`:${key}`, encodeURIComponent(value));
    });
    
    return formattedEndpoint;
  },

  /**
   * Build query string from parameters
   */
  buildQueryString: (params: Record<string, any>): string => {
    const searchParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          value.forEach(v => searchParams.append(key, v.toString()));
        } else {
          searchParams.append(key, value.toString());
        }
      }
    });
    
    return searchParams.toString();
  },

  /**
   * Debounce function for search operations
   */
  debounce: <T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): ((...args: Parameters<T>) => void) => {
    let timeout: NodeJS.Timeout;
    
    return (...args: Parameters<T>) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func(...args), wait);
    };
  },

  /**
   * Retry function with exponential backoff
   */
  retry: async <T>(
    fn: () => Promise<T>,
    attempts: number = 3,
    delay: number = 1000
  ): Promise<T> => {
    try {
      return await fn();
    } catch (error) {
      if (attempts <= 1) {
        throw error;
      }
      
      await new Promise(resolve => setTimeout(resolve, delay));
      return serviceUtils.retry(fn, attempts - 1, delay * 2);
    }
  },

  /**
   * Cache manager for client-side caching
   */
  cache: {
    get: (key: string): any => {
      try {
        const item = localStorage.getItem(`cache_${key}`);
        if (item) {
          const parsed = JSON.parse(item);
          if (parsed.expiry && Date.now() > parsed.expiry) {
            localStorage.removeItem(`cache_${key}`);
            return null;
          }
          return parsed.data;
        }
      } catch (error) {
        console.warn('Cache get error:', error);
      }
      return null;
    },

    set: (key: string, data: any, ttl: number = 300000): void => { // 5 minutes default
      try {
        const item = {
          data,
          expiry: Date.now() + ttl,
        };
        localStorage.setItem(`cache_${key}`, JSON.stringify(item));
      } catch (error) {
        console.warn('Cache set error:', error);
      }
    },

    remove: (key: string): void => {
      localStorage.removeItem(`cache_${key}`);
    },

    clear: (): void => {
      Object.keys(localStorage).forEach(key => {
        if (key.startsWith('cache_')) {
          localStorage.removeItem(key);
        }
      });
    },
  },
};
