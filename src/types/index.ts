// User types
export interface User {
  id: string;
  username: string;
  email: string;
  avatar?: string;
  role: "reader" | "moderator" | "admin";
  createdAt: string;
  updatedAt: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
}

// Manga types
export interface Manga {
  id: string;
  title: string;
  alternativeTitle?: string;
  description: string;
  coverImage: string;
  author: string;
  artist?: string;
  status: "ongoing" | "completed" | "hiatus" | "cancelled";
  genres: string[];
  tags: string[];
  rating: number;
  ratingCount: number;
  chaptersCount: number;
  viewCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface Chapter {
  id: string;
  mangaId: string;
  title: string;
  chapterNumber: number;
  pages: string[];
  publishedAt: string;
  viewCount: number;
}

// User interaction types
export interface Bookmark {
  id: string;
  userId: string;
  mangaId: string;
  createdAt: string;
}

export interface ReadingHistory {
  id: string;
  userId: string;
  mangaId: string;
  chapterId: string;
  pageNumber: number;
  readAt: string;
}

export interface Comment {
  id: string;
  userId: string;
  mangaId: string;
  content: string;
  rating?: number;
  createdAt: string;
  updatedAt: string;
  user: Pick<User, "id" | "username" | "avatar">;
}

// Reader types
export interface ReaderSettings {
  readingMode: "single" | "double" | "webtoon";
  readingDirection: "ltr" | "rtl";
  pageFit: "width" | "height" | "auto";
  brightness: number;
  backgroundColor: string;
}

// HTTP Method types
export type HttpMethod =
  | "GET"
  | "POST"
  | "PUT"
  | "DELETE"
  | "PATCH"
  | "HEAD"
  | "OPTIONS";

// API Configuration types
export interface RequestConfig {
  method: HttpMethod;
  url: string;
  headers?: Record<string, string>;
  timeout: number;
  retries?: number;
  retryDelay?: number;
}

// API Error types
export interface ApiError {
  name?: string;
  status: number;
  statusText: string;
  message: string;
  data?: any;
  url?: string;
  method?: HttpMethod;
}

// API Response types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

// OTruyenAPI specific response format
export interface OTruyenApiResponse<T> {
  status: boolean;
  message?: string;
  data?: T;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// OTruyenAPI specific paginated response
export interface OTruyenPaginatedResponse<T> {
  status: boolean;
  message?: string;
  data: {
    items: T[];
    params: {
      pagination: {
        totalItems: number;
        totalItemsPerPage: number;
        currentPage: number;
        totalPages: number;
      };
    };
  };
}

export interface MangaFilters {
  search?: string;
  genres?: string[];
  status?: string;
  sortBy?: "title" | "rating" | "views" | "updated" | "created";
  sortOrder?: "asc" | "desc";
  page?: number;
  limit?: number;
}
